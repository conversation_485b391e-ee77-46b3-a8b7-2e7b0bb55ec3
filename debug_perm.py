def debug_permutations(current, remaining, level=0):
    """Debug version to see what's happening"""
    indent = "  " * level
    print(f"{indent}Called with: current='{current}', remaining='{remaining}'")
    
    if len(remaining) == 0:
        print(f"{indent}BASE CASE: Found permutation: '{current}'")
        return
    
    for i in range(len(remaining)):
        chosen = remaining[i]
        new_remaining = remaining[:i] + remaining[i+1:]
        print(f"{indent}Choosing '{chosen}', new_remaining='{new_remaining}'")
        debug_permutations(current + chosen, new_remaining, level + 1)

print("=== DEBUGGING PERMUTATION ALGORITHM ===")
debug_permutations("", "AB")  # Start with simpler case
print("\n" + "="*40)
