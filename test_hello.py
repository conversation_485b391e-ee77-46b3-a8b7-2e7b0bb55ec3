#!/usr/bin/env python3
"""
Simple test file to verify Python setup is working correctly.
"""

def main():
    print("Hello, World!")
    print("Python is working correctly in your DSAPython workspace!")
    
    # Test some basic functionality
    numbers = [1, 2, 3, 4, 5]
    squared = [x**2 for x in numbers]
    print(f"Original numbers: {numbers}")
    print(f"Squared numbers: {squared}")
    
    # Test input (commented out for automatic running)
    # name = input("Enter your name: ")
    # print(f"Hello, {name}!")

if __name__ == "__main__":
    main()
