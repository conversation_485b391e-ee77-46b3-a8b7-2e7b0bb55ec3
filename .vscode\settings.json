{"python.defaultInterpreterPath": "C:\\Program Files\\Python38\\python.exe", "python.terminal.activateEnvironment": true, "code-runner.executorMap": {"python": "cd $dir && python -u $fileName"}, "code-runner.runInTerminal": true, "code-runner.saveFileBeforeRun": true, "code-runner.clearPreviousOutput": true, "code-runner.preserveFocus": false, "code-runner.showExecutionMessage": true, "code-runner.ignoreSelection": false, "files.defaultLanguage": "python", "python.linting.enabled": false, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": false, "python.formatting.provider": "none", "terminal.integrated.defaultProfile.windows": "Command Prompt", "terminal.integrated.profiles.windows": {"Command Prompt": {"path": "cmd.exe", "args": []}}, "python.terminal.executeInFileDir": true, "python.terminal.launchArgs": ["-u"]}