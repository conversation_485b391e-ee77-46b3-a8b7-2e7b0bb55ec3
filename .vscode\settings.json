{"python.defaultInterpreterPath": "C:\\Program Files\\Python38\\python.exe", "python.terminal.activateEnvironment": true, "code-runner.executorMap": {"python": "python -u \"$fullFileName\" && pause"}, "code-runner.runInTerminal": true, "code-runner.saveFileBeforeRun": true, "code-runner.clearPreviousOutput": true, "code-runner.preserveFocus": false, "code-runner.showExecutionMessage": true, "files.defaultLanguage": "python", "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.formatting.provider": "black", "terminal.integrated.shell.windows": "cmd.exe"}