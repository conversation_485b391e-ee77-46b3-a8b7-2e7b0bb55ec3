def factorial(n):
    """Calculate factorial of n using recursion"""
    if n <= 1:
        return 1
    else:
        return n * factorial(n - 1)

# Test the function
print("=== Factorial Calculator ===")
print(f"factorial(0) = {factorial(0)}")
print(f"factorial(1) = {factorial(1)}")
print(f"factorial(5) = {factorial(5)}")
print(f"factorial(10) = {factorial(10)}")
print("All tests completed successfully!")
