#!/usr/bin/env python3

print("=" * 50)
print("🐍 PYTHON IS WORKING! 🐍")
print("=" * 50)

# Test basic operations
print("Testing basic operations:")
print(f"2 + 3 = {2 + 3}")
print(f"10 * 5 = {10 * 5}")

# Test a simple function
def greet(name):
    return f"Hello, {name}!"

print(f"\nFunction test: {greet('World')}")

# Test the fixed factorial function
def factorial(n):
    if n <= 1:
        return 1
    else:
        return n * factorial(n - 1)

print(f"\nFactorial tests:")
print(f"factorial(5) = {factorial(5)}")
print(f"factorial(3) = {factorial(3)}")

print("\n" + "=" * 50)
print("✅ ALL TESTS PASSED!")
print("=" * 50)

input("Press Enter to continue...")
