#!/usr/bin/env python3
"""
Test script to verify VS Code run button works properly
"""

print("=" * 50)
print("🚀 TESTING VS CODE RUN BUTTON")
print("=" * 50)

# Test 1: Basic output
print("✅ Test 1: Basic print statements work")
print("Hello, World!")

# Test 2: Variables and operations
print("\n✅ Test 2: Variables and operations")
x = 10
y = 20
print(f"x = {x}, y = {y}")
print(f"x + y = {x + y}")

# Test 3: Functions
print("\n✅ Test 3: Function calls")
def greet(name):
    return f"Hello, {name}!"

print(greet("Python Developer"))

# Test 4: Loops
print("\n✅ Test 4: Loops")
for i in range(1, 4):
    print(f"Loop iteration: {i}")

# Test 5: Error handling
print("\n✅ Test 5: Error handling")
try:
    result = 10 / 2
    print(f"Division result: {result}")
except Exception as e:
    print(f"Error: {e}")

print("\n" + "=" * 50)
print("🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
print("✅ VS Code run button is working properly")
print("=" * 50)

# Keep window open for a moment
import time
print("\nWindow will close in 3 seconds...")
time.sleep(3)
