class Student:
    def __init__(self, args):
        self.name = args[0]
        self.age = args[1]
        self.rollno = args[2]
    
    def display(self):
        print(f"name:{self.name} age:{self.age} rollno:{self.rollno}")


class Marks(Student):
    def __init__(self, args, marks):
        super().__init__(args)
        self.marks = marks
        print("child class")
    
    def displaymarks(self):
        self.display()
        print(f"marks:{self.marks}")


class Report(Marks):
    def __init__(self, args, marks):
        super().__init__(args, marks)
    
    def displayreport(self):
        per=(sum(self.marks)/5)*100
        self.grade = 'A' if per >= 90 else 'B' if per >= 80 else 'C'
        self.displaymarks()
        print(f"grade:{self.grade}")

m = Report("raju 21 1002".split(), [90, 45, 60, 70, 80])
m.displayreport()