class Student:
    def __init__(self, args):
        self.name = args[0]
        self.age = args[1]
        self.rollno = args[2]
    
    def display(self):
        print(f"name:{self.name} age:{self.age} rollno:{self.rollno}")


class Marks(Student):
    def __init__(self, args, marks):
        super().__init__(args)
        self.marks = marks
        print("child class")
    
    def displaymarks(self):
        self.display()
        print(f"marks:{self.marks}")


class Report(Marks):
    def __init__(self, args, marks):
        super().__init__(args, marks)
        self.report = 0  # Initialize report
        print("2child class")
    
    def displayreport(self):
        sum_marks = 0
        for i in self.marks:
            sum_marks += i
        self.report = sum_marks / len(self.marks)
        
        if self.report > 90:
            print("grade:A")
        elif self.report > 80:
            print("grade:B")
        elif self.report > 70:
            print("grade:C")
        elif self.report > 60:
            print("grade:D")
        else:
            print("grade:F")
        
        self.displaymarks()
        print(f"report:{self.report}")


m = Report("raju 21 1002".split(), [90, 45, 60, 70, 80])
m.displayreport()