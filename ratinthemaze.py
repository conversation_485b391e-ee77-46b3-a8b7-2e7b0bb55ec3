def isValid(maze , visited,r,c):
    if(r<0 or c<0 or r>=len(maze) or c>= len(maze[0]) or maze[r][c]==0 or visited[r][c]==1) :
        maze[r][c]== 0 or visited[r][c]==1):
        return False
    return True
def RatInMaze(maze,visited,r,c):
    if r==3 and c==3:
        visited[r][c]=1
        return True
    directions=[[-1,0],[+1,0],[0,-1],[0,+1]]
    print(r,c)
    for direction in directions:
        row,col=direction
        newrow,newcol=row+r,col+c
        if(isValid(maze,visited,newrow,newcol)):
            visited[newrow][newcol]=1
            if(RatInMaze(maze,visited,newrow,newcol)):
                return True
        visited[row][col]=0
    return False
maze=([1,0,0,0],[1,1,0,1],[0,1,0,0],[1,1,1,1])
