import sys

def permutations(current, remaining):
    """Generate all permutations"""
    if len(remaining) == 0:
        print(current, flush=True)
        sys.stdout.flush()
        return
    
    for i in range(len(remaining)):
        chosen = remaining[i]
        new_remaining = remaining[:i] + remaining[i+1:]
        permutations(current + chosen, new_remaining)

print("All permutations of 'ABC':", flush=True)
sys.stdout.flush()
permutations("", "ABC")
print("Done!", flush=True)
sys.stdout.flush()
